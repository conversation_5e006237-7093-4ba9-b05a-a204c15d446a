#!/usr/bin/env python3
"""
股票预警系统测试脚本
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestConfig(unittest.TestCase):
    """配置模块测试"""
    
    def test_config_import(self):
        """测试配置模块导入"""
        try:
            from config import config
            self.assertIsNotNone(config)
        except Exception as e:
            self.fail(f"配置模块导入失败: {e}")
    
    def test_config_attributes(self):
        """测试配置属性"""
        from config import config
        
        # 检查必要的配置属性是否存在
        required_attrs = [
            'NEWS_CHECK_INTERVAL',
            'STOCK_CHECK_INTERVAL', 
            'DAILY_DROP_THRESHOLD',
            'SHORT_DROP_THRESHOLD',
            'NEWS_KEYWORDS',
            'STOCK_INDICES'
        ]
        
        for attr in required_attrs:
            self.assertTrue(hasattr(config, attr), f"缺少配置属性: {attr}")

class TestNewsDataFetcher(unittest.TestCase):
    """新闻数据抓取模块测试"""
    
    def test_news_fetcher_import(self):
        """测试新闻抓取模块导入"""
        try:
            from news_fetcher import NewsDataFetcher
            fetcher = NewsDataFetcher()
            self.assertIsNotNone(fetcher)
        except Exception as e:
            self.fail(f"新闻抓取模块导入失败: {e}")
    
    def test_contains_keywords(self):
        """测试关键词检测功能"""
        from news_fetcher import NewsDataFetcher
        fetcher = NewsDataFetcher()
        
        # 测试包含关键词的文本
        self.assertTrue(fetcher._contains_keywords("俄乌冲突最新消息"))
        self.assertTrue(fetcher._contains_keywords("中美贸易谈判进展"))
        self.assertTrue(fetcher._contains_keywords("央行降准政策"))
        
        # 测试不包含关键词的文本
        self.assertFalse(fetcher._contains_keywords("今天天气很好"))
        self.assertFalse(fetcher._contains_keywords("股票涨跌正常"))

class TestStockDataFetcher(unittest.TestCase):
    """股票数据获取模块测试"""
    
    def test_stock_fetcher_import(self):
        """测试股票数据获取模块导入"""
        try:
            from stock_monitor import StockDataFetcher
            fetcher = StockDataFetcher()
            self.assertIsNotNone(fetcher)
        except Exception as e:
            self.fail(f"股票数据获取模块导入失败: {e}")

class TestStockAnalyzer(unittest.TestCase):
    """股票分析模块测试"""
    
    def test_analyzer_import(self):
        """测试股票分析模块导入"""
        try:
            from stock_monitor import StockAnalyzer
            analyzer = StockAnalyzer()
            self.assertIsNotNone(analyzer)
        except Exception as e:
            self.fail(f"股票分析模块导入失败: {e}")
    
    def test_analyze_a_share_indices(self):
        """测试A股指数分析"""
        from stock_monitor import StockAnalyzer
        analyzer = StockAnalyzer()
        
        # 模拟测试数据
        test_data = [
            {
                'name': '上证指数',
                'change_pct': -3.5,  # 大幅下跌
                'current_price': 3000
            },
            {
                'name': '深证成指',
                'change_pct': 1.2,   # 正常涨幅
                'current_price': 10000
            }
        ]
        
        alerts = analyzer._analyze_a_share_indices(test_data)
        
        # 应该有一个下跌预警
        self.assertGreater(len(alerts), 0)
        self.assertEqual(alerts[0]['index_name'], '上证指数')
        self.assertEqual(alerts[0]['type'], '股指预警')

class TestEmailSender(unittest.TestCase):
    """邮件发送模块测试"""
    
    def test_email_sender_import(self):
        """测试邮件发送模块导入"""
        try:
            from email_sender import EmailSender
            sender = EmailSender()
            self.assertIsNotNone(sender)
        except Exception as e:
            self.fail(f"邮件发送模块导入失败: {e}")
    
    def test_build_email_body(self):
        """测试邮件正文构建"""
        from email_sender import EmailSender
        sender = EmailSender()
        
        body = sender._build_email_body(
            alert_type="测试预警",
            title="测试标题",
            content="测试内容",
            urgency="中等"
        )
        
        self.assertIn("测试预警", body)
        self.assertIn("测试标题", body)
        self.assertIn("测试内容", body)
        self.assertIn("中等", body)

class TestTaskScheduler(unittest.TestCase):
    """任务调度器测试"""
    
    def test_scheduler_import(self):
        """测试调度器模块导入"""
        try:
            from scheduler import TaskScheduler
            scheduler = TaskScheduler()
            self.assertIsNotNone(scheduler)
        except Exception as e:
            self.fail(f"调度器模块导入失败: {e}")
    
    def test_is_trading_time(self):
        """测试交易时间判断"""
        from scheduler import TaskScheduler
        scheduler = TaskScheduler()
        
        # 这个测试依赖于当前时间，所以只检查方法是否能正常调用
        try:
            result = scheduler._is_trading_time()
            self.assertIsInstance(result, bool)
        except Exception as e:
            self.fail(f"交易时间判断失败: {e}")

def run_integration_tests():
    """运行集成测试"""
    print("=" * 50)
    print("开始运行集成测试...")
    print("=" * 50)
    
    # 测试新闻抓取功能
    print("\n1. 测试新闻抓取功能...")
    try:
        from news_fetcher import news_fetcher
        news_list = news_fetcher.fetch_sina_news(max_news=3)
        print(f"   ✅ 新浪财经新闻抓取成功，获取 {len(news_list)} 条新闻")
        
        eastmoney_news = news_fetcher.fetch_eastmoney_news(max_news=3)
        print(f"   ✅ 东方财富新闻抓取成功，获取 {len(eastmoney_news)} 条新闻")
        
    except Exception as e:
        print(f"   ❌ 新闻抓取测试失败: {e}")
    
    # 测试股票数据获取功能
    print("\n2. 测试股票数据获取功能...")
    try:
        from stock_monitor import stock_data_fetcher
        
        a_share_data = stock_data_fetcher.get_a_share_indices()
        print(f"   ✅ A股指数数据获取成功，获取 {len(a_share_data)} 条数据")
        
        international_data = stock_data_fetcher.get_international_indices()
        print(f"   ✅ 国际指数数据获取成功，获取 {len(international_data)} 条数据")
        
    except Exception as e:
        print(f"   ❌ 股票数据获取测试失败: {e}")
    
    # 测试数据分析功能
    print("\n3. 测试数据分析功能...")
    try:
        from stock_monitor import stock_analyzer
        
        # 创建模拟数据
        mock_data = {
            'a_share_indices': [
                {'name': '上证指数', 'change_pct': -1.5, 'current_price': 3000}
            ],
            'international_indices': [
                {'name': '标普500', 'market': '美股', 'change_pct': -0.8}
            ]
        }
        
        analysis_result = stock_analyzer.analyze_market_changes(mock_data)
        print(f"   ✅ 市场数据分析成功，生成 {len(analysis_result.get('alerts', []))} 条预警")
        
    except Exception as e:
        print(f"   ❌ 数据分析测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("集成测试完成")
    print("=" * 50)

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == "--integration":
        # 运行集成测试
        run_integration_tests()
        return
    
    # 运行单元测试
    print("=" * 50)
    print("开始运行单元测试...")
    print("=" * 50)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestConfig,
        TestNewsDataFetcher,
        TestStockDataFetcher,
        TestStockAnalyzer,
        TestEmailSender,
        TestTaskScheduler
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("单元测试结果:")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    print("=" * 50)
    
    # 如果有失败或错误，返回非零退出码
    if result.failures or result.errors:
        sys.exit(1)
    else:
        print("✅ 所有测试通过！")

if __name__ == "__main__":
    main()

