import requests
import logging
import json
import os
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import re
import time
from config import config

class NewsDataFetcher:
    """新闻数据抓取类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.history_file = 'news_history.json'
        self.history_expiry_hours = 24  # 历史记录保留24小时
        
    def fetch_sina_news(self, max_news=20):
        """抓取新浪财经新闻"""
        try:
            url = "https://finance.sina.com.cn"
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_list = []
            seen_urls = set()  # URL去重
            
            # 查找新闻链接
            news_links = soup.find_all('a', href=re.compile(r'/\d{4}-\d{2}-\d{2}/doc-'))
            
            for link in news_links:
                # 如果已达到最大数量，退出循环
                if len(news_list) >= max_news:
                    break
                    
                try:
                    title = link.get_text().strip()
                    if not title or len(title) < 5:
                        continue
                        
                    href = link.get('href')
                    if href.startswith('/'):
                        full_url = "https://finance.sina.com.cn" + href
                    else:
                        full_url = href
                    
                    # URL去重检查
                    if full_url in seen_urls:
                        continue
                    seen_urls.add(full_url)
                    
                    # 检查是否包含关键词
                    if self._contains_keywords(title):
                        news_content = self._fetch_news_content(full_url)
                        if news_content:
                            news_list.append({
                                'title': title,
                                'url': full_url,
                                'content': news_content,
                                'source': '新浪财经',
                                'timestamp': datetime.now()
                            })
                            
                except Exception as e:
                    self.logger.warning(f"处理新浪新闻链接失败: {e}")
                    continue
            
            self.logger.info(f"成功抓取新浪财经新闻 {len(news_list)} 条")
            return news_list
            
        except Exception as e:
            self.logger.error(f"抓取新浪财经新闻失败: {e}")
            return []
    
    def fetch_eastmoney_news(self, max_news=20):
        """抓取东方财富新闻"""
        try:
            url = "https://finance.eastmoney.com"
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            news_list = []
            seen_urls = set()  # URL去重
            
            # 查找新闻链接
            news_links = soup.find_all('a', href=re.compile(r'finance\.eastmoney\.com'))
            
            for link in news_links:
                # 如果已达到最大数量，退出循环
                if len(news_list) >= max_news:
                    break
                    
                try:
                    title = link.get_text().strip()
                    if not title or len(title) < 5:
                        continue
                        
                    href = link.get('href')
                    if not href.startswith('http'):
                        continue
                    
                    # URL去重检查
                    if href in seen_urls:
                        continue
                    seen_urls.add(href)
                    
                    # 检查是否包含关键词
                    if self._contains_keywords(title):
                        news_content = self._fetch_news_content(href)
                        if news_content:
                            news_list.append({
                                'title': title,
                                'url': href,
                                'content': news_content,
                                'source': '东方财富',
                                'timestamp': datetime.now()
                            })
                            
                except Exception as e:
                    self.logger.warning(f"处理东方财富新闻链接失败: {e}")
                    continue
            
            self.logger.info(f"成功抓取东方财富新闻 {len(news_list)} 条")
            return news_list
            
        except Exception as e:
            self.logger.error(f"抓取东方财富新闻失败: {e}")
            return []
    
    def _contains_keywords(self, text):
        """检查文本是否包含关键词"""
        for keyword in config.NEWS_KEYWORDS:
            if keyword in text:
                return True
        return False
    
    def _fetch_news_content(self, url):
        """获取新闻详细内容"""
        try:
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'

            soup = BeautifulSoup(response.text, 'html.parser')

            # 针对不同网站的特殊处理
            if 'finance.sina.com.cn' in url:
                content = self._fetch_sina_content(soup)
            elif 'finance.eastmoney.com' in url:
                content = self._fetch_eastmoney_content(soup)
            else:
                # 其他网站使用通用方法
                content = self._fetch_generic_content(soup)

            # 清理内容
            if content:
                content = re.sub(r'\s+', ' ', content)

            return content if content and len(content) > 50 else None

        except Exception as e:
            self.logger.warning(f"获取新闻内容失败 {url}: {e}")
            return None

    def _fetch_sina_content(self, soup):
        """获取新浪财经新闻内容"""
        try:
            # 查找主要内容区域
            main_content = soup.select_one('.main-content')
            if not main_content:
                return self._fetch_generic_content(soup)

            # 移除脚本和样式标签
            for script in main_content(["script", "style"]):
                script.decompose()

            # 移除"热点栏目"相关的元素
            hot_elements = main_content.find_all(text=lambda text: text and '热点栏目' in text)
            for element in hot_elements:
                parent = element.parent
                while parent and parent != main_content:
                    # 找到包含"热点栏目"的容器并移除
                    if parent.name in ['div', 'section', 'aside']:
                        parent.decompose()
                        break
                    parent = parent.parent

            # 获取所有段落
            paragraphs = main_content.find_all('p')
            content_parts = []

            for p in paragraphs:
                text = p.get_text().strip()
                if text:
                    # 跳过"资料来源："之后的所有内容
                    if '资料来源：' in text or '资料来源:' in text:
                        break

                    # 跳过"24小时滚动播报"之后的所有内容
                    if '24小时滚动播报' in text:
                        break

                    # 跳过"声明："开头的内容
                    if text.startswith('声明：') or text.startswith('声明:'):
                        break

                    # 过滤掉包含APP下载推广的段落
                    if any(keyword in text for keyword in [
                        '下载新浪财经APP', '海量资讯、精准解读，尽在新浪财经APP',
                        '24小时滚动播报最新的财经资讯和视频', '更多粉丝福利扫描二维码关注'
                    ]):
                        continue

                    # 过滤掉包含"热点栏目"、"自选股"、"数据中心"等导航链接的段落
                    if any(keyword in text for keyword in [
                        '热点栏目', '自选股', '数据中心', '行情中心', '资金流向',
                        '模拟交易', '客户端', '新浪简介', '广告服务', 'About Sina',
                        '联系我们', '招聘信息', '通行证注册', '产品答疑', '网站律师',
                        'SINA English', 'Copyright', 'All Rights Reserved'
                    ]):
                        continue

                    # 过滤掉太短的段落（可能是导航或广告）
                    if len(text) < 10:
                        continue

                    # 过滤掉责任编辑信息
                    if text.startswith('责任编辑：'):
                        continue

                    content_parts.append(text)

            return '\n'.join(content_parts) if content_parts else None

        except Exception as e:
            self.logger.warning(f"获取新浪财经内容失败: {e}")
            return self._fetch_generic_content(soup)

    def _fetch_generic_content(self, soup):
        """获取通用网站内容"""
        try:
            # 尝试多种内容选择器
            content_selectors = [
                '.article-content',
                '.content',
                '.news-content',
                '.article-body',
                '.main-content',
                'div[class*="content"]',
                'div[class*="article"]'
            ]

            content = ""
            for selector in content_selectors:
                content_div = soup.select_one(selector)
                if content_div:
                    # 移除脚本和样式标签
                    for script in content_div(["script", "style"]):
                        script.decompose()

                    content = content_div.get_text().strip()
                    if len(content) > 100:  # 确保内容足够长
                        break

            # 如果没有找到内容，尝试获取所有段落
            if not content or len(content) < 100:
                paragraphs = soup.find_all('p')
                content = '\n'.join([p.get_text().strip() for p in paragraphs if p.get_text().strip()])

            return content

        except Exception as e:
            self.logger.warning(f"获取通用内容失败: {e}")
            return None

    def _fetch_eastmoney_content(self, soup):
        """获取东方财富新闻内容"""
        try:
            # 查找主要内容区域
            main_content = soup.select_one('.txtinfos')
            if not main_content:
                return self._fetch_generic_content(soup)

            # 获取内容区域内的所有段落
            paragraphs = main_content.find_all('p')
            content_parts = []

            for p in paragraphs:
                text = p.get_text().strip()
                if text and len(text) > 5:
                    # 跳过推广内容（通常包含"在东方财富看资讯行情"等）
                    if any(keyword in text for keyword in [
                        '在东方财富看资讯行情', '选东方财富证券', '一站式开户交易',
                        '全新妙想投研助理', '立即体验', '扫一扫下载APP'
                    ]):
                        continue

                    # 跳过文章来源信息（通常有em_media类名或包含"文章来源"）
                    if (p.get('class') and 'em_media' in p.get('class')) or '文章来源' in text:
                        continue

                    # 跳过页脚和版权信息
                    if any(keyword in text for keyword in [
                        '责任编辑', '郑重声明', '原标题', '举报', '分享到',
                        '网友评论', '登录', '注册', '即可将网页分享至朋友圈',
                        '沪ICP证', '版权所有', '违法和不良信息举报'
                    ]):
                        continue

                    # 跳过导航链接（通常很短且包含导航关键词）
                    if len(text) < 15 and any(keyword in text for keyword in [
                        '财经', '焦点', '股票', '新股', '期指', '期权',
                        '行情', '数据', '全球', '美股', '港股', '期货',
                        '外汇', '银行', '基金', '理财', '债券', '直播',
                        '股吧', '基金吧', '博客', '财富号', '搜索'
                    ]):
                        continue

                    content_parts.append(text)

            return '\n'.join(content_parts) if content_parts else None

        except Exception as e:
            self.logger.warning(f"获取东方财富内容失败: {e}")
            return self._fetch_generic_content(soup)

    def _load_news_history(self):
        """加载新闻历史记录"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)

                # 清理过期记录
                current_time = datetime.now()
                valid_history = {}

                for url, timestamp_str in history_data.items():
                    try:
                        timestamp = datetime.fromisoformat(timestamp_str)
                        if (current_time - timestamp).total_seconds() < self.history_expiry_hours * 3600:
                            valid_history[url] = timestamp_str
                    except ValueError:
                        # 忽略无效的时间戳
                        continue

                # 如果有记录被清理，更新文件
                if len(valid_history) != len(history_data):
                    self._save_news_history(valid_history)
                    self.logger.info(f"清理了 {len(history_data) - len(valid_history)} 条过期的新闻记录")

                return set(valid_history.keys())
            else:
                return set()
        except Exception as e:
            self.logger.warning(f"加载新闻历史记录失败: {e}")
            return set()

    def _save_news_history(self, history_dict):
        """保存新闻历史记录"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history_dict, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.warning(f"保存新闻历史记录失败: {e}")

    def _add_to_history(self, urls):
        """添加URL到历史记录"""
        try:
            # 加载现有历史
            history_data = {}
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history_data = json.load(f)

            # 添加新URL
            current_time = datetime.now().isoformat()
            for url in urls:
                history_data[url] = current_time

            # 保存更新后的历史
            self._save_news_history(history_data)

        except Exception as e:
            self.logger.warning(f"添加新闻到历史记录失败: {e}")

    def fetch_all_news(self):
        """抓取所有新闻源的新闻"""
        # 加载历史记录
        processed_urls = self._load_news_history()
        self.logger.info(f"加载了 {len(processed_urls)} 条历史新闻记录")

        all_news = []

        # 抓取东方财富新闻
        eastmoney_news = self.fetch_eastmoney_news()
        all_news.extend(eastmoney_news)

        sina_news = self.fetch_sina_news()
        all_news.extend(sina_news)

        # 基于URL和标题的双重去重
        unique_news = []
        seen_titles = set()
        seen_urls = set()
        new_urls = []

        for news in all_news:
            url = news['url']
            title = news['title']

            # 跳过已处理过的URL
            if url in processed_urls:
                continue

            # 跳过重复的URL和标题
            if url not in seen_urls and title not in seen_titles:
                unique_news.append(news)
                seen_titles.add(title)
                seen_urls.add(url)
                new_urls.append(url)

        # 将新的URL添加到历史记录
        if new_urls:
            self._add_to_history(new_urls)
            self.logger.info(f"添加了 {len(new_urls)} 条新闻到历史记录")

        self.logger.info(f"总共抓取到 {len(unique_news)} 条新的相关新闻（过滤了 {len(all_news) - len(unique_news)} 条重复或已处理的新闻）")
        return unique_news

# 全局新闻抓取实例
news_fetcher = NewsDataFetcher()


