import logging
import openai
from datetime import datetime
from config import config

class NewsAnalyzer:
    """新闻分析类，使用Azure OpenAI进行智能分析"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self._setup_openai_client()
    
    def _setup_openai_client(self):
        """设置Azure OpenAI客户端"""
        try:
            self.client = openai.AzureOpenAI(
                api_key=config.AZURE_OPENAI_KEY,
                api_version=config.AZURE_OPENAI_API_VERSION,
                azure_endpoint=config.AZURE_OPENAI_ENDPOINT
            )
            self.logger.info("Azure OpenAI客户端初始化成功")
        except Exception as e:
            self.logger.error(f"Azure OpenAI客户端初始化失败: {e}")
    
    def analyze_news_impact(self, news_list):
        """分析新闻对股市的影响

        Args:
            news_list: 新闻列表，每个元素包含title, content, source等字段

        Returns:
            dict: 分析结果，包含影响程度、建议等
        """
        if not news_list:
            return {
                'impact_level': '无影响',
                'recommendation': '无特殊建议',
                'analysis': '暂无相关新闻',
                'urgency': '低'
            }

        try:
            # 第一步：从新闻标题中选出重要且去重的新闻
            selected_news = self._select_important_news(news_list)

            if not selected_news:
                return {
                    'impact_level': '无影响',
                    'recommendation': '无特殊建议',
                    'analysis': '未发现重要新闻',
                    'urgency': '低'
                }

            # 第二步：为选出的新闻生成详细摘要
            news_with_summaries = self._generate_news_summaries(selected_news)

            # 第三步：基于摘要进行影响分析
            news_summary = self._build_enhanced_news_summary(news_with_summaries)
            prompt = self._build_analysis_prompt(news_summary)

            # 调用Azure OpenAI进行分析
            response = self.client.chat.completions.create(
                model=config.AZURE_OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的股市分析师，专门分析新闻事件对A股市场的影响。请基于提供的新闻内容，给出专业的分析和建议。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1000
            )

            analysis_result = response.choices[0].message.content

            # 解析分析结果
            parsed_result = self._parse_analysis_result(analysis_result)

            self.logger.info(f"新闻分析完成，影响程度: {parsed_result['impact_level']}")
            return parsed_result

        except Exception as e:
            self.logger.error(f"新闻分析失败: {e}")
            return {
                'impact_level': '未知',
                'recommendation': '建议人工判断',
                'analysis': f'分析过程出现错误: {str(e)}',
                'urgency': '中等'
            }

    def _select_important_news(self, news_list):
        """使用LLM从新闻标题中选出重要且去重的新闻"""
        try:
            # 构建新闻标题列表
            news_titles = []
            for i, news in enumerate(news_list):
                news_titles.append(f"{i+1}. 【{news['source']}】{news['title']}")

            titles_text = "\n".join(news_titles)

            # 构建选择提示词
            selection_prompt = f"""
请从以下新闻标题中选出可能包含重要财经信息的新闻，要求：
1. 去除重复或相似的新闻
2. 优先选择可能对股市有重大影响的新闻
3. 关注政策、经济数据、重大事件、行业动态等
4. 最多选择8条新闻
5. 请只返回选中新闻的序号，用逗号分隔，如：1,3,5,7

新闻标题列表：
{titles_text}

请返回选中的新闻序号："""

            response = self.client.chat.completions.create(
                model=config.AZURE_OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的财经新闻筛选专家，能够识别对股市有重要影响的新闻。"
                    },
                    {
                        "role": "user",
                        "content": selection_prompt
                    }
                ],
                temperature=0.1,
                max_tokens=100
            )

            # 解析选中的新闻序号
            selected_indices_text = response.choices[0].message.content.strip()
            selected_indices = []

            try:
                # 提取数字
                import re
                numbers = re.findall(r'\d+', selected_indices_text)
                selected_indices = [int(num) - 1 for num in numbers if 0 <= int(num) - 1 < len(news_list)]
            except:
                # 如果解析失败，选择前5条新闻
                selected_indices = list(range(min(5, len(news_list))))

            # 返回选中的新闻
            selected_news = [news_list[i] for i in selected_indices]

            self.logger.info(f"从{len(news_list)}条新闻中选出{len(selected_news)}条重要新闻")
            return selected_news

        except Exception as e:
            self.logger.warning(f"新闻选择失败，使用前5条新闻: {e}")
            return news_list[:5]

    def _generate_news_summaries(self, selected_news):
        """为选中的新闻生成详细摘要"""
        news_with_summaries = []

        for news in selected_news:
            try:
                # 构建摘要生成提示词
                content = news.get('content', news['title'])
                summary_prompt = f"""
请为以下新闻生成一个约300字的专业摘要，要求：
1. 保留所有重要信息和关键数据
2. 尽量数字化表达，包含具体的数据、百分比、金额等
3. 突出对股市可能的影响
4. 语言简洁专业

新闻标题：{news['title']}
新闻来源：{news['source']}
新闻内容：{content}

请生成摘要："""

                response = self.client.chat.completions.create(
                    model=config.AZURE_OPENAI_MODEL,
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一位专业的财经新闻分析师，擅长提取新闻中的关键信息并进行数字化表达。"
                        },
                        {
                            "role": "user",
                            "content": summary_prompt
                        }
                    ],
                    temperature=0.2,
                    max_tokens=500
                )

                summary = response.choices[0].message.content.strip()

                # 创建包含摘要的新闻对象
                news_with_summary = news.copy()
                news_with_summary['ai_summary'] = summary
                news_with_summaries.append(news_with_summary)

            except Exception as e:
                self.logger.warning(f"生成新闻摘要失败: {e}")
                # 如果生成摘要失败，使用原始内容的前300字符
                news_with_summary = news.copy()
                content = news.get('content', news['title'])
                news_with_summary['ai_summary'] = content[:300] + "..." if len(content) > 300 else content
                news_with_summaries.append(news_with_summary)

        self.logger.info(f"为{len(news_with_summaries)}条新闻生成了AI摘要")
        return news_with_summaries

    def _build_enhanced_news_summary(self, news_with_summaries):
        """构建增强的新闻摘要（使用AI生成的摘要）"""
        summary = "重要财经新闻AI分析摘要：\n\n"

        for i, news in enumerate(news_with_summaries, 1):
            summary += f"{i}. 【{news['source']}】{news['title']}\n"
            summary += f"   AI摘要: {news['ai_summary']}\n"
            summary += f"   时间: {news['timestamp'].strftime('%Y-%m-%d %H:%M')}\n\n"

        return summary

    def _build_news_summary(self, news_list):
        """构建新闻摘要"""
        summary = "最新财经新闻摘要：\n\n"
        
        for i, news in enumerate(news_list[:10], 1):  # 最多分析10条新闻
            summary += f"{i}. 【{news['source']}】{news['title']}\n"
            if news.get('content'):
                # 截取内容前300字符
                content_preview = news['content'][:300] + "..." if len(news['content']) > 300 else news['content']
                summary += f"   内容摘要: {content_preview}\n"
            summary += f"   时间: {news['timestamp'].strftime('%Y-%m-%d %H:%M')}\n\n"
        
        return summary
    
    def _build_analysis_prompt(self, news_summary):
        """构建分析提示词"""
        prompt = f"""
请分析以下财经新闻对A股市场的影响，并给出专业建议：

{news_summary}

请从以下几个维度进行分析：

1. **影响程度评估**（请选择：重大利空、中等利空、轻微利空、中性、轻微利好、中等利好、重大利好）
2. **紧急程度**（请选择：低、中等、高、紧急）
3. **具体影响分析**（详细说明这些新闻可能对A股各板块的影响）
4. **投资建议**（给出具体的操作建议，如减仓、观望、加仓等）
5. **风险提示**（指出需要特别关注的风险点）

请用以下格式回复：
影响程度：[选择一个]
紧急程度：[选择一个]
分析：[详细分析内容]
建议：[具体投资建议]
风险：[风险提示]
"""
        return prompt
    
    def _parse_analysis_result(self, analysis_text):
        """解析分析结果"""
        try:
            lines = analysis_text.strip().split('\n')
            result = {
                'impact_level': '中性',
                'urgency': '中等',
                'analysis': '',
                'recommendation': '',
                'risks': ''
            }
            
            current_section = None
            content_buffer = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是新的部分
                if line.startswith('影响程度：') or line.startswith('影响程度:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    result['impact_level'] = line.split('：')[-1].split(':')[-1].strip()
                    current_section = None
                elif line.startswith('紧急程度：') or line.startswith('紧急程度:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    result['urgency'] = line.split('：')[-1].split(':')[-1].strip()
                    current_section = None
                elif line.startswith('分析：') or line.startswith('分析:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'analysis'
                    analysis_content = line.split('：')[-1].split(':')[-1].strip()
                    if analysis_content:
                        content_buffer.append(analysis_content)
                elif line.startswith('建议：') or line.startswith('建议:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'recommendation'
                    rec_content = line.split('：')[-1].split(':')[-1].strip()
                    if rec_content:
                        content_buffer.append(rec_content)
                elif line.startswith('风险：') or line.startswith('风险:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'risks'
                    risk_content = line.split('：')[-1].split(':')[-1].strip()
                    if risk_content:
                        content_buffer.append(risk_content)
                else:
                    # 继续当前部分的内容
                    if current_section:
                        content_buffer.append(line)
            
            # 处理最后一个部分
            if current_section and content_buffer:
                result[current_section] = '\n'.join(content_buffer)
            
            # 如果没有解析到内容，使用原始文本
            if not result['analysis']:
                result['analysis'] = analysis_text
            
            return result
            
        except Exception as e:
            self.logger.warning(f"解析分析结果失败: {e}")
            return {
                'impact_level': '中性',
                'urgency': '中等',
                'analysis': analysis_text,
                'recommendation': '建议人工判断',
                'risks': '解析失败，请人工检查'
            }
    
    def should_trigger_alert(self, analysis_result):
        """判断是否应该触发预警"""
        impact_level = analysis_result.get('impact_level', '中性')
        urgency = analysis_result.get('urgency', '中等')
        
        # 定义触发预警的条件
        high_impact_keywords = ['重大利空', '中等利空', '重大利好']
        high_urgency_keywords = ['高', '紧急']
        
        # 如果影响程度较大或紧急程度较高，则触发预警
        if any(keyword in impact_level for keyword in high_impact_keywords):
            return True
        
        if any(keyword in urgency for keyword in high_urgency_keywords):
            return True
        
        return False

# 全局新闻分析实例
news_analyzer = NewsAnalyzer()

