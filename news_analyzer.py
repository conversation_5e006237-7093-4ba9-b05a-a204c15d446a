import logging
import openai
from datetime import datetime
from config import config

class NewsAnalyzer:
    """新闻分析类，使用Azure OpenAI进行智能分析"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.client = None
        self._setup_openai_client()
    
    def _setup_openai_client(self):
        """设置Azure OpenAI客户端"""
        try:
            self.client = openai.AzureOpenAI(
                api_key=config.AZURE_OPENAI_KEY,
                api_version=config.AZURE_OPENAI_API_VERSION,
                azure_endpoint=config.AZURE_OPENAI_ENDPOINT
            )
            self.logger.info("Azure OpenAI客户端初始化成功")
        except Exception as e:
            self.logger.error(f"Azure OpenAI客户端初始化失败: {e}")
    
    def analyze_news_impact(self, news_list):
        """分析新闻对股市的影响
        
        Args:
            news_list: 新闻列表，每个元素包含title, content, source等字段
            
        Returns:
            dict: 分析结果，包含影响程度、建议等
        """
        if not news_list:
            return {
                'impact_level': '无影响',
                'recommendation': '无特殊建议',
                'analysis': '暂无相关新闻',
                'urgency': '低'
            }
        
        try:
            # 构建分析提示词
            news_summary = self._build_news_summary(news_list)
            prompt = self._build_analysis_prompt(news_summary)
            
            # 调用Azure OpenAI进行分析
            response = self.client.chat.completions.create(
                model=config.AZURE_OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的股市分析师，专门分析新闻事件对A股市场的影响。请基于提供的新闻内容，给出专业的分析和建议。"
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            analysis_result = response.choices[0].message.content
            
            # 解析分析结果
            parsed_result = self._parse_analysis_result(analysis_result)
            
            self.logger.info(f"新闻分析完成，影响程度: {parsed_result['impact_level']}")
            return parsed_result
            
        except Exception as e:
            self.logger.error(f"新闻分析失败: {e}")
            return {
                'impact_level': '未知',
                'recommendation': '建议人工判断',
                'analysis': f'分析过程出现错误: {str(e)}',
                'urgency': '中等'
            }
    
    def _build_news_summary(self, news_list):
        """构建新闻摘要"""
        summary = "最新财经新闻摘要：\n\n"
        
        for i, news in enumerate(news_list[:10], 1):  # 最多分析10条新闻
            summary += f"{i}. 【{news['source']}】{news['title']}\n"
            if news.get('content'):
                # 截取内容前300字符
                content_preview = news['content'][:300] + "..." if len(news['content']) > 300 else news['content']
                summary += f"   内容摘要: {content_preview}\n"
            summary += f"   时间: {news['timestamp'].strftime('%Y-%m-%d %H:%M')}\n\n"
        
        return summary
    
    def _build_analysis_prompt(self, news_summary):
        """构建分析提示词"""
        prompt = f"""
请分析以下财经新闻对A股市场的影响，并给出专业建议：

{news_summary}

请从以下几个维度进行分析：

1. **影响程度评估**（请选择：重大利空、中等利空、轻微利空、中性、轻微利好、中等利好、重大利好）
2. **紧急程度**（请选择：低、中等、高、紧急）
3. **具体影响分析**（详细说明这些新闻可能对A股各板块的影响）
4. **投资建议**（给出具体的操作建议，如减仓、观望、加仓等）
5. **风险提示**（指出需要特别关注的风险点）

请用以下格式回复：
影响程度：[选择一个]
紧急程度：[选择一个]
分析：[详细分析内容]
建议：[具体投资建议]
风险：[风险提示]
"""
        return prompt
    
    def _parse_analysis_result(self, analysis_text):
        """解析分析结果"""
        try:
            lines = analysis_text.strip().split('\n')
            result = {
                'impact_level': '中性',
                'urgency': '中等',
                'analysis': '',
                'recommendation': '',
                'risks': ''
            }
            
            current_section = None
            content_buffer = []
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 检查是否是新的部分
                if line.startswith('影响程度：') or line.startswith('影响程度:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    result['impact_level'] = line.split('：')[-1].split(':')[-1].strip()
                    current_section = None
                elif line.startswith('紧急程度：') or line.startswith('紧急程度:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    result['urgency'] = line.split('：')[-1].split(':')[-1].strip()
                    current_section = None
                elif line.startswith('分析：') or line.startswith('分析:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'analysis'
                    analysis_content = line.split('：')[-1].split(':')[-1].strip()
                    if analysis_content:
                        content_buffer.append(analysis_content)
                elif line.startswith('建议：') or line.startswith('建议:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'recommendation'
                    rec_content = line.split('：')[-1].split(':')[-1].strip()
                    if rec_content:
                        content_buffer.append(rec_content)
                elif line.startswith('风险：') or line.startswith('风险:'):
                    if current_section and content_buffer:
                        result[current_section] = '\n'.join(content_buffer)
                        content_buffer = []
                    current_section = 'risks'
                    risk_content = line.split('：')[-1].split(':')[-1].strip()
                    if risk_content:
                        content_buffer.append(risk_content)
                else:
                    # 继续当前部分的内容
                    if current_section:
                        content_buffer.append(line)
            
            # 处理最后一个部分
            if current_section and content_buffer:
                result[current_section] = '\n'.join(content_buffer)
            
            # 如果没有解析到内容，使用原始文本
            if not result['analysis']:
                result['analysis'] = analysis_text
            
            return result
            
        except Exception as e:
            self.logger.warning(f"解析分析结果失败: {e}")
            return {
                'impact_level': '中性',
                'urgency': '中等',
                'analysis': analysis_text,
                'recommendation': '建议人工判断',
                'risks': '解析失败，请人工检查'
            }
    
    def should_trigger_alert(self, analysis_result):
        """判断是否应该触发预警"""
        impact_level = analysis_result.get('impact_level', '中性')
        urgency = analysis_result.get('urgency', '中等')
        
        # 定义触发预警的条件
        high_impact_keywords = ['重大利空', '中等利空', '重大利好']
        high_urgency_keywords = ['高', '紧急']
        
        # 如果影响程度较大或紧急程度较高，则触发预警
        if any(keyword in impact_level for keyword in high_impact_keywords):
            return True
        
        if any(keyword in urgency for keyword in high_urgency_keywords):
            return True
        
        return False

# 全局新闻分析实例
news_analyzer = NewsAnalyzer()

