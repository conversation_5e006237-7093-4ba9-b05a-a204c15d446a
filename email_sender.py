import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header
from datetime import datetime
from config import config

class EmailSender:
    """邮件发送类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.smtp_server = None
        
    def connect(self):
        """连接邮件服务器"""
        try:
            self.smtp_server = smtplib.SMTP(config.EMAIL_HOST, config.EMAIL_PORT)
            self.smtp_server.starttls()
            self.smtp_server.login(config.EMAIL_USER, config.EMAIL_PASSWORD)
            self.logger.info("邮件服务器连接成功")
            return True
        except Exception as e:
            self.logger.error(f"邮件服务器连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开邮件服务器连接"""
        if self.smtp_server:
            try:
                self.smtp_server.quit()
                self.logger.info("邮件服务器连接已断开")
            except Exception as e:
                self.logger.error(f"断开邮件服务器连接失败: {e}")
    
    def send_alert_email(self, alert_type, title, content, urgency="中等"):
        """发送预警邮件
        
        Args:
            alert_type: 预警类型（新闻预警、股指预警、开盘前预警）
            title: 邮件标题
            content: 邮件内容
            urgency: 紧急程度（低、中等、高、紧急）
        """
        try:
            if not self.connect():
                return False
            
            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = Header(f"股票预警系统 <{config.EMAIL_USER}>", 'utf-8')
            msg['To'] = Header(config.EMAIL_TO, 'utf-8')
            
            # 根据紧急程度设置邮件标题前缀
            urgency_prefix = {
                "低": "📊",
                "中等": "⚠️",
                "高": "🚨",
                "紧急": "🔴"
            }
            
            subject = f"{urgency_prefix.get(urgency, '⚠️')} [{alert_type}] {title}"
            msg['Subject'] = Header(subject, 'utf-8')
            
            # 构建邮件正文
            email_body = self._build_email_body(alert_type, title, content, urgency)
            msg.attach(MIMEText(email_body, 'html', 'utf-8'))
            
            # 发送邮件
            self.smtp_server.send_message(msg)
            self.logger.info(f"预警邮件发送成功: {title}")
            
            self.disconnect()
            return True
            
        except Exception as e:
            self.logger.error(f"发送预警邮件失败: {e}")
            self.disconnect()
            return False
    
    def _build_email_body(self, alert_type, title, content, urgency):
        """构建邮件正文HTML"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 根据紧急程度设置颜色
        urgency_colors = {
            "低": "#28a745",
            "中等": "#ffc107", 
            "高": "#fd7e14",
            "紧急": "#dc3545"
        }
        
        color = urgency_colors.get(urgency, "#ffc107")
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .header {{ background-color: {color}; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; }}
                .alert-type {{ background-color: #f8f9fa; padding: 10px; border-left: 4px solid {color}; margin: 15px 0; }}
                .timestamp {{ color: #666; font-size: 12px; text-align: right; margin-top: 20px; }}
                .footer {{ background-color: #f8f9fa; padding: 15px; text-align: center; color: #666; font-size: 12px; }}
                .urgency {{ display: inline-block; background-color: {color}; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 股票预警系统</h1>
                    <p>A股投资风险提醒</p>
                </div>
                <div class="content">
                    <div class="alert-type">
                        <strong>预警类型:</strong> {alert_type} 
                        <span class="urgency">{urgency}</span>
                    </div>
                    <h2>{title}</h2>
                    <div style="line-height: 1.6; color: #333;">
                        {content.replace(chr(10), '<br>')}
                    </div>
                    <div class="timestamp">
                        预警时间: {current_time}
                    </div>
                </div>
                <div class="footer">
                    <p>此邮件由股票预警系统自动发送，请及时关注市场动态</p>
                    <p>如需停止接收预警邮件，请联系系统管理员</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_body
    
    def send_test_email(self):
        """发送测试邮件"""
        test_content = """
        这是一封测试邮件，用于验证邮件发送功能是否正常。
        
        系统配置信息：
        - 邮件服务器: {host}:{port}
        - 发送账号: {user}
        - 接收账号: {to}
        
        如果您收到此邮件，说明邮件发送功能配置正确。
        """.format(
            host=config.EMAIL_HOST,
            port=config.EMAIL_PORT,
            user=config.EMAIL_USER,
            to=config.EMAIL_TO
        )
        
        return self.send_alert_email(
            alert_type="系统测试",
            title="邮件发送功能测试",
            content=test_content,
            urgency="低"
        )

# 全局邮件发送实例
email_sender = EmailSender()

