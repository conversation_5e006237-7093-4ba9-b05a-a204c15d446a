import schedule
import time
import threading
import logging
from datetime import datetime, time as dt_time
from config import config
from news_fetcher import news_fetcher
from news_analyzer import news_analyzer
from stock_monitor import stock_data_fetcher, stock_analyzer
from email_sender import email_sender

class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.scheduler_thread = None
        
    def start(self):
        """启动调度器"""
        if self.running:
            self.logger.warning("调度器已在运行中")
            return
        
        self.running = True
        self._setup_schedules()
        
        # 在单独线程中运行调度器
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        self.logger.info("任务调度器已启动")
    
    def stop(self):
        """停止调度器"""
        self.running = False
        schedule.clear()
        self.logger.info("任务调度器已停止")
    
    def _setup_schedules(self):
        """设置定时任务"""
        # 新闻监控任务 - 每10分钟执行一次
        schedule.every(config.NEWS_CHECK_INTERVAL).minutes.do(self._news_monitoring_task)
        
        # 股指监控任务 - 每5分钟执行一次（交易时间内）
        schedule.every(config.STOCK_CHECK_INTERVAL).minutes.do(self._stock_monitoring_task)
        
        # 开盘前预警任务 - 每天9:00执行
        schedule.every().day.at("09:00").do(self._pre_market_analysis_task)
        
        # 收盘后总结任务 - 每天15:30执行
        schedule.every().day.at("15:30").do(self._post_market_summary_task)
        
        # 系统健康检查 - 每小时执行一次
        schedule.every().hour.do(self._system_health_check)
        
        self.logger.info("定时任务已配置完成")
    
    def _run_scheduler(self):
        """运行调度器主循环"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                self.logger.error(f"调度器运行错误: {e}")
                time.sleep(5)
    
    def _news_monitoring_task(self):
        """新闻监控任务"""
        try:
            self.logger.info("开始执行新闻监控任务")
            
            # 获取新闻
            news_list = news_fetcher.fetch_all_news()
            
            if news_list:
                # 分析新闻影响
                analysis_result = news_analyzer.analyze_news_impact(news_list)
                
                # 判断是否需要发送预警
                if news_analyzer.should_trigger_alert(analysis_result):
                    self._send_news_alert(news_list, analysis_result)
                else:
                    self.logger.info("新闻分析完成，无需发送预警")
            else:
                self.logger.info("未获取到相关新闻")
                
        except Exception as e:
            self.logger.error(f"新闻监控任务执行失败: {e}")
    
    def _stock_monitoring_task(self):
        """股指监控任务"""
        try:
            # 检查是否在交易时间内
            if not self._is_trading_time():
                return
            
            self.logger.info("开始执行股指监控任务")
            
            # 获取市场数据
            market_data = stock_data_fetcher.get_all_market_data()
            
            # 分析市场变化
            analysis_result = stock_analyzer.analyze_market_changes(market_data)
            
            # 判断是否需要发送预警
            if stock_analyzer.should_trigger_alert(analysis_result):
                self._send_stock_alert(market_data, analysis_result)
            else:
                self.logger.info("股指监控完成，无异常情况")
                
        except Exception as e:
            self.logger.error(f"股指监控任务执行失败: {e}")
    
    def _pre_market_analysis_task(self):
        """开盘前分析任务"""
        try:
            self.logger.info("开始执行开盘前分析任务")
            
            # 获取国际市场数据
            international_data = stock_data_fetcher.get_international_indices()
            
            if international_data:
                # 分析国际市场对A股的影响
                analysis_result = stock_analyzer.analyze_market_changes({
                    'international_indices': international_data,
                    'a_share_indices': []
                })
                
                # 发送开盘前提醒
                self._send_pre_market_alert(international_data, analysis_result)
            
        except Exception as e:
            self.logger.error(f"开盘前分析任务执行失败: {e}")
    
    def _post_market_summary_task(self):
        """收盘后总结任务"""
        try:
            self.logger.info("开始执行收盘后总结任务")
            
            # 获取当日市场数据
            market_data = stock_data_fetcher.get_all_market_data()
            
            # 生成日度总结
            summary = self._generate_daily_summary(market_data)
            
            # 发送总结邮件
            email_sender.send_alert_email(
                alert_type="日度总结",
                title=f"{datetime.now().strftime('%Y-%m-%d')} 市场总结",
                content=summary,
                urgency="低"
            )
            
        except Exception as e:
            self.logger.error(f"收盘后总结任务执行失败: {e}")
    
    def _system_health_check(self):
        """系统健康检查"""
        try:
            self.logger.info("执行系统健康检查")
            
            # 检查各模块状态
            health_status = {
                'news_fetcher': self._check_news_fetcher_health(),
                'stock_data': self._check_stock_data_health(),
                'email_service': self._check_email_service_health(),
                'openai_service': self._check_openai_service_health()
            }
            
            # 如果有模块异常，发送告警
            failed_modules = [module for module, status in health_status.items() if not status]
            if failed_modules:
                self.logger.warning(f"系统模块异常: {failed_modules}")
                # 可以选择发送系统告警邮件
            
        except Exception as e:
            self.logger.error(f"系统健康检查失败: {e}")
    
    def _send_news_alert(self, news_list, analysis_result):
        """发送新闻预警邮件"""
        try:
            # 构建邮件内容
            content = f"""
新闻分析结果：

影响程度：{analysis_result.get('impact_level', '未知')}
紧急程度：{analysis_result.get('urgency', '中等')}

分析详情：
{analysis_result.get('analysis', '暂无详细分析')}

投资建议：
{analysis_result.get('recommendation', '暂无建议')}

风险提示：
{analysis_result.get('risks', '暂无风险提示')}

相关新闻：
"""
            
            for i, news in enumerate(news_list[:5], 1):
                content += f"\n{i}. 【{news['source']}】{news['title']}\n   链接：{news['url']}\n"
            
            # 发送邮件
            email_sender.send_alert_email(
                alert_type="新闻预警",
                title=f"重要财经新闻预警 - {analysis_result.get('impact_level', '影响待定')}",
                content=content,
                urgency=analysis_result.get('urgency', '中等')
            )
            
            self.logger.info("新闻预警邮件发送成功")
            
        except Exception as e:
            self.logger.error(f"发送新闻预警邮件失败: {e}")
    
    def _send_stock_alert(self, market_data, analysis_result):
        """发送股指预警邮件"""
        try:
            alerts = analysis_result.get('alerts', [])
            
            # 构建邮件内容
            content = f"""
市场分析总结：
{analysis_result.get('summary', '暂无总结')}

预警详情：
"""
            
            for i, alert in enumerate(alerts, 1):
                content += f"\n{i}. {alert.get('title', '未知预警')}\n"
                content += f"   类型：{alert.get('type', '未知')}\n"
                content += f"   详情：{alert.get('message', '暂无详情')}\n"
                content += f"   紧急程度：{alert.get('urgency', '中等')}\n"
            
            content += f"\n投资建议：\n"
            for rec in analysis_result.get('recommendations', []):
                content += f"• {rec}\n"
            
            # 发送邮件
            email_sender.send_alert_email(
                alert_type="股指预警",
                title=f"股市异常预警 - {len(alerts)}项异常",
                content=content,
                urgency=analysis_result.get('urgency', '中等')
            )
            
            self.logger.info("股指预警邮件发送成功")
            
        except Exception as e:
            self.logger.error(f"发送股指预警邮件失败: {e}")
    
    def _send_pre_market_alert(self, international_data, analysis_result):
        """发送开盘前预警邮件"""
        try:
            content = f"""
开盘前国际市场分析：

国际市场表现：
"""
            
            for market in international_data:
                content += f"• {market['name']} ({market['market']})：{market['change_pct']:.2f}%\n"
            
            content += f"\n对A股影响分析：\n{analysis_result.get('summary', '暂无分析')}\n"
            
            if analysis_result.get('recommendations'):
                content += f"\n开盘建议：\n"
                for rec in analysis_result['recommendations']:
                    content += f"• {rec}\n"
            
            # 发送邮件
            email_sender.send_alert_email(
                alert_type="开盘前预警",
                title=f"开盘前市场分析 - {datetime.now().strftime('%Y-%m-%d')}",
                content=content,
                urgency=analysis_result.get('urgency', '低')
            )
            
            self.logger.info("开盘前预警邮件发送成功")
            
        except Exception as e:
            self.logger.error(f"发送开盘前预警邮件失败: {e}")
    
    def _generate_daily_summary(self, market_data):
        """生成日度总结"""
        try:
            summary = f"市场日度总结 - {datetime.now().strftime('%Y-%m-%d')}\n\n"
            
            # A股指数表现
            a_share_data = market_data.get('a_share_indices', [])
            if a_share_data:
                summary += "A股主要指数表现：\n"
                for index in a_share_data:
                    summary += f"• {index['name']}：{index['change_pct']:.2f}%\n"
                summary += "\n"
            
            # 国际市场表现
            international_data = market_data.get('international_indices', [])
            if international_data:
                summary += "国际市场表现：\n"
                for market in international_data:
                    summary += f"• {market['name']}：{market['change_pct']:.2f}%\n"
                summary += "\n"
            
            summary += "感谢您使用股票预警系统，祝您投资顺利！"
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成日度总结失败: {e}")
            return "日度总结生成失败，请检查系统状态。"
    
    def _is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()
        current_time = now.time()
        
        # A股交易时间：9:30-11:30, 13:00-15:00
        morning_start = dt_time(9, 30)
        morning_end = dt_time(11, 30)
        afternoon_start = dt_time(13, 0)
        afternoon_end = dt_time(15, 0)
        
        # 检查是否为工作日（周一到周五）
        if now.weekday() >= 5:  # 周六、周日
            return False
        
        # 检查是否在交易时间内
        if (morning_start <= current_time <= morning_end) or \
           (afternoon_start <= current_time <= afternoon_end):
            return True
        
        return False
    
    def _check_news_fetcher_health(self):
        """检查新闻抓取模块健康状态"""
        try:
            # 尝试获取少量新闻测试
            test_news = news_fetcher.fetch_sina_news(max_news=1)
            return True
        except:
            return False
    
    def _check_stock_data_health(self):
        """检查股票数据模块健康状态"""
        try:
            # 尝试获取股指数据测试
            test_data = stock_data_fetcher.get_a_share_indices()
            return len(test_data) > 0
        except:
            return False
    
    def _check_email_service_health(self):
        """检查邮件服务健康状态"""
        try:
            # 尝试连接邮件服务器
            return email_sender.connect()
        except:
            return False
    
    def _check_openai_service_health(self):
        """检查OpenAI服务健康状态"""
        try:
            # 检查客户端是否正常初始化
            return news_analyzer.client is not None
        except:
            return False

# 全局调度器实例
task_scheduler = TaskScheduler()

