# 股票预警系统使用说明

## 系统概述

股票预警系统是一个基于Python的自动化监控系统，用于分析新闻和市场数据，通过Azure OpenAI判断利好利空程度，并发送邮件提醒用户及时卖出A股股票。

### 主要功能

1. **新闻监控**：自动抓取新浪财经、东方财富等网站的财经新闻
2. **智能分析**：使用Azure OpenAI分析新闻对股市的影响程度
3. **股指监控**：实时监控A股主要指数和国际市场指数
4. **预警通知**：通过邮件发送预警信息和投资建议
5. **定时任务**：支持多种定时监控任务和开盘前分析

### 监控范围

- **新闻类型**：经济数据、政策发布、战争冲突、贸易政策等
- **股票指数**：上证指数、深证成指、创业板指、沪深300、中证500
- **国际市场**：美股、港股、A50期货、日韩台股市
- **预警条件**：日跌幅超过2%、10分钟内跌幅超过1%

## 系统要求

### 环境要求
- Python 3.8+
- 稳定的网络连接
- 邮箱账号（支持SMTP）

### 必需的API密钥
- Azure OpenAI API密钥和端点
- 网易邮箱或其他SMTP邮箱账号

## 安装部署

### 1. 下载系统代码

将系统代码下载到本地目录：

```bash
# 假设代码已下载到 stock_alert_system 目录
cd stock_alert_system
```

### 2. 安装依赖包

```bash
pip3 install -r requirements.txt
```

### 3. 配置环境变量

复制配置文件模板：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填写必要的配置信息：

```bash
# Azure OpenAI配置
AZURE_OPENAI_KEY=your_azure_openai_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_MODEL=gpt-4
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# 邮件配置（网易邮箱）
EMAIL_HOST=smtp.163.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password_or_app_password
EMAIL_TO=<EMAIL>

# 监控配置
NEWS_CHECK_INTERVAL=10
STOCK_CHECK_INTERVAL=5
DAILY_DROP_THRESHOLD=2.0
SHORT_DROP_THRESHOLD=1.0

# 日志级别
LOG_LEVEL=INFO
```

### 4. 运行测试

测试系统各模块功能：

```bash
# 测试邮件发送
python3 main.py --test-email

# 测试新闻抓取
python3 main.py --test-news

# 测试股票数据获取
python3 main.py --test-stock

# 运行完整测试
python3 test_system.py
```

### 5. 启动系统

```bash
python3 main.py
```

## 配置说明

### 邮件配置

系统支持网易邮箱（推荐）和其他SMTP邮箱：

#### 网易邮箱配置
1. 登录网易邮箱，开启SMTP服务
2. 生成授权码（不是登录密码）
3. 在 `.env` 文件中填写邮箱和授权码

#### 其他邮箱配置
- **QQ邮箱**：`smtp.qq.com:587`
- **Gmail**：`smtp.gmail.com:587`
- **企业邮箱**：根据邮箱提供商配置

### Azure OpenAI配置

1. 在Azure门户创建OpenAI资源
2. 获取API密钥和端点URL
3. 部署GPT-4模型
4. 在 `.env` 文件中填写相关信息

### 监控参数配置

- `NEWS_CHECK_INTERVAL`：新闻检查间隔（分钟）
- `STOCK_CHECK_INTERVAL`：股指检查间隔（分钟）
- `DAILY_DROP_THRESHOLD`：日跌幅预警阈值（百分比）
- `SHORT_DROP_THRESHOLD`：短期跌幅预警阈值（百分比）

## 使用指南

### 启动系统

```bash
python3 main.py
```

系统启动后会：
1. 验证配置文件
2. 发送启动通知邮件
3. 开始定时监控任务
4. 在控制台显示运行状态

### 停止系统

按 `Ctrl+C` 或发送 SIGTERM 信号停止系统。

### 查看日志

系统日志保存在 `stock_alert.log` 文件中：

```bash
tail -f stock_alert.log
```

### 系统服务化部署（可选）

将系统安装为Linux系统服务：

```bash
# 运行部署脚本
./deploy.sh

# 安装为系统服务
sudo cp stock-alert.service /etc/systemd/system/
sudo systemctl enable stock-alert
sudo systemctl start stock-alert

# 查看服务状态
sudo systemctl status stock-alert
```

## 预警类型

### 1. 新闻预警

当检测到重要财经新闻时触发：
- 重大经济数据发布
- 央行政策变化
- 国际冲突事件
- 贸易政策调整

### 2. 股指预警

当股指出现异常波动时触发：
- 日跌幅超过设定阈值
- 短期内急剧下跌
- 成交量异常

### 3. 国际市场预警

当国际市场出现重大变化时触发：
- 美股大幅下跌
- 港股异常波动
- A50期货大跌

### 4. 开盘前预警

每日开盘前分析国际市场对A股的影响：
- 隔夜美股表现
- 亚太市场走势
- 期货市场信号

## 邮件通知

### 邮件类型

1. **系统通知**：启动/停止通知
2. **新闻预警**：重要新闻分析
3. **股指预警**：股市异常波动
4. **开盘前预警**：开盘前市场分析
5. **日度总结**：每日市场总结

### 紧急程度

- **低**：一般信息通知
- **中等**：需要关注的市场变化
- **高**：重要预警信息
- **紧急**：需要立即关注的重大事件

## 故障排除

### 常见问题

#### 1. 邮件发送失败
- 检查邮箱配置是否正确
- 确认SMTP服务已开启
- 验证授权码是否正确

#### 2. 新闻抓取失败
- 检查网络连接
- 确认目标网站可访问
- 查看日志了解具体错误

#### 3. 股票数据获取失败
- 检查akshare库是否正常安装
- 确认网络连接稳定
- 查看是否有API限制

#### 4. Azure OpenAI调用失败
- 检查API密钥是否正确
- 确认端点URL格式正确
- 验证模型部署状态

### 日志分析

系统日志包含详细的运行信息：

```bash
# 查看最新日志
tail -n 100 stock_alert.log

# 搜索错误信息
grep "ERROR" stock_alert.log

# 搜索预警信息
grep "预警" stock_alert.log
```

### 性能优化

1. **调整监控频率**：根据需要调整检查间隔
2. **限制新闻数量**：避免抓取过多新闻影响性能
3. **优化网络请求**：设置合适的超时时间
4. **定期清理日志**：避免日志文件过大

## 系统架构

### 核心模块

1. **config.py**：配置管理
2. **news_fetcher.py**：新闻数据抓取
3. **news_analyzer.py**：新闻智能分析
4. **stock_monitor.py**：股指数据监控
5. **email_sender.py**：邮件发送服务
6. **scheduler.py**：定时任务调度
7. **main.py**：系统主程序

### 数据流程

```
新闻源 → 新闻抓取 → 内容解析 → AI分析 → 预警判断 → 邮件通知
股票API → 数据获取 → 变化分析 → 预警判断 → 邮件通知
定时器 → 任务调度 → 模块协调 → 状态监控 → 异常处理
```

## 扩展开发

### 添加新的新闻源

1. 在 `news_fetcher.py` 中添加新的抓取方法
2. 更新 `fetch_all_news()` 方法
3. 测试新闻源的可用性

### 添加新的股票数据源

1. 在 `stock_monitor.py` 中添加新的数据获取方法
2. 更新分析逻辑
3. 测试数据源的稳定性

### 自定义预警规则

1. 修改 `stock_analyzer.py` 中的分析逻辑
2. 调整预警阈值
3. 添加新的预警类型

## 安全注意事项

1. **保护API密钥**：不要将密钥提交到版本控制系统
2. **邮箱安全**：使用授权码而非登录密码
3. **网络安全**：在安全的网络环境中运行
4. **数据隐私**：注意保护个人投资信息

## 免责声明

本系统仅供参考，不构成投资建议。投资有风险，决策需谨慎。使用者应根据自身情况做出投资决策，系统开发者不承担任何投资损失责任。

## 技术支持

如遇到技术问题，请：
1. 查看系统日志文件
2. 运行测试脚本诊断问题
3. 检查配置文件是否正确
4. 确认网络连接和API服务状态

## 版本信息

- **版本**：1.0.0
- **开发语言**：Python 3.11
- **主要依赖**：akshare, openai, schedule, beautifulsoup4
- **支持平台**：Linux, macOS, Windows

