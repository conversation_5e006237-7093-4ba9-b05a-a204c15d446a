import os
import logging
from dotenv import load_dotenv

class Config:
    """系统配置管理类"""
    
    def __init__(self):
        # 加载环境变量
        load_dotenv()
        
        # Azure OpenAI配置
        self.AZURE_OPENAI_KEY = os.getenv('AZURE_OPENAI_KEY')
        self.AZURE_OPENAI_ENDPOINT = os.getenv('AZURE_OPENAI_ENDPOINT')
        self.AZURE_OPENAI_MODEL = os.getenv('AZURE_OPENAI_MODEL', 'gpt-4')
        self.AZURE_OPENAI_API_VERSION = os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview')
        
        # 邮件配置
        self.EMAIL_HOST = os.getenv('EMAIL_HOST', 'smtp.163.com')
        self.EMAIL_PORT = int(os.getenv('EMAIL_PORT', '587'))
        self.EMAIL_USER = os.getenv('EMAIL_USER')
        self.EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
        self.EMAIL_TO = os.getenv('EMAIL_TO')
        
        # 监控配置
        self.NEWS_CHECK_INTERVAL = int(os.getenv('NEWS_CHECK_INTERVAL', '10'))  # 分钟
        self.STOCK_CHECK_INTERVAL = int(os.getenv('STOCK_CHECK_INTERVAL', '5'))  # 分钟
        self.DAILY_DROP_THRESHOLD = float(os.getenv('DAILY_DROP_THRESHOLD', '2.0'))  # 百分比
        self.SHORT_DROP_THRESHOLD = float(os.getenv('SHORT_DROP_THRESHOLD', '1.0'))  # 百分比
        
        # Tushare配置（备用）
        self.TUSHARE_TOKEN = os.getenv('TUSHARE_TOKEN')
        
        # 新闻源配置
        self.NEWS_SOURCES = {
            'sina': 'https://finance.sina.com.cn',
            'eastmoney': 'https://finance.eastmoney.com'
        }
        
        # 关键词配置
        self.NEWS_KEYWORDS = [
            # 宏观经济指标
            'GDP', 'CPI', 'PPI', 'PMI', '工业增加值', '社会消费品零售总额',
            '固定资产投资', '进出口', '贸易顺差', '外汇储备', 'M2', '社融',
            
            # 货币政策
            '央行', '货币政策', '利率', '降准', '降息', '加息', '存款准备金率',
            'MLF', 'LPR', '逆回购', '公开市场操作', '量化宽松', '缩表',
            
            # 财政政策
            '财政政策', '减税降费', '专项债', '财政赤字', '政府工作报告',
            '预算', '财政支出', '财政补贴',
            
            # 国际关系与地缘政治
            '中美关系', '中美贸易', '贸易战', '关税', '制裁', '反制',
            '俄乌冲突', '俄乌战争', '乌克兰', '俄罗斯',
            '以色列', '巴勒斯坦', '伊朗', '中东冲突',
            '南海', '台海', '朝鲜', '半岛局势', '袭击', '轰炸'
            
            # 国际金融市场
            '美联储', 'FOMC', '鲍威尔', '欧洲央行', '日本央行', '英国央行',
            '道琼斯', '纳斯达克', '标普500', '恒生指数', '日经指数',
            '美元指数', '人民币汇率', '欧元汇率', '日元汇率',
            
            # 行业政策
            '房地产', '楼市', '限购', '限贷', '调控',
            '新能源', '光伏', '风电', '储能', '氢能',
            '芯片', '半导体', '集成电路', '人工智能', 'AI',
            '医药', '医保', '集采', '创新药',
            '汽车', '新能源车', '智能驾驶',
            
            # 重大事件
            '疫情', '新冠', '疫苗', '防控',
            '自然灾害', '地震', '洪水', '台风',
            '重大', '事故', '环境污染',
            
            # 监管政策
            '证监会', '银保监会', '央行', '金融监管',
            '反垄断', '反不正当竞争', '数据安全', '网络安全',
            'IPO', '注册制', '退市', '并购重组',
            
            # 市场情绪
            '牛市', '熊市', '震荡', '调整', '反弹', '回调',
            '北向资金', '南向资金', '外资', '机构', '散户',
            '成交量', '换手率', '融资融券', '杠杆',
            
            # 大宗商品
            '原油', '黄金', '铜', '铝', '铁矿石', '煤炭',
            '农产品', '大豆', '玉米', '小麦', '猪肉',
            
            # 科技创新
            '5G', '6G', '量子计算', '区块链', '元宇宙',
            'ChatGPT', '大模型', '生成式AI', '算力',
            
            # 区域发展
            '粤港澳大湾区', '长三角', '京津冀', '成渝双城',
            '自贸区', '经济特区', '国家级新区',
            
            # 重大会议
            '中央经济工作会议', '两会', '政治局会议',
            'G20', 'APEC', '达沃斯论坛', '博鳌论坛'
        ]
        
        # 股指代码配置
        self.STOCK_INDICES = {
            'sh': '000001',  # 上证指数
            'sz': '399001',  # 深证成指
            'cyb': '399006', # 创业板指
            'hs300': '000300', # 沪深300
            'zz500': '000905'  # 中证500
        }
        
        # 国际指数配置
        self.INTERNATIONAL_INDICES = {
            'dow': '^DJI',     # 道琼斯
            'sp500': '^GSPC',  # 标普500
            'nasdaq': '^IXIC', # 纳斯达克
            'hsi': '^HSI',     # 恒生指数
            'nikkei': '^N225', # 日经225
            'kospi': '^KS11'   # 韩国综合指数
        }
        
        # 日志配置
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        logging.basicConfig(
            level=getattr(logging, log_level),
            format=log_format,
            handlers=[
                logging.FileHandler('stock_alert.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
    
    def validate_config(self):
        """验证配置完整性"""
        required_configs = [
            'AZURE_OPENAI_KEY',
            'AZURE_OPENAI_ENDPOINT',
            'EMAIL_USER',
            'EMAIL_PASSWORD',
            'EMAIL_TO'
        ]
        
        missing_configs = []
        for config in required_configs:
            if not getattr(self, config):
                missing_configs.append(config)
        
        if missing_configs:
            raise ValueError(f"缺少必要配置: {', '.join(missing_configs)}")
        
        return True

# 全局配置实例
config = Config()

