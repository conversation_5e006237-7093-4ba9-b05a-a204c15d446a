#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修改后的新闻分析器
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from news_analyzer import NewsAnalyzer

def test_news_analyzer():
    """测试新闻分析器的新功能"""
    
    # 创建测试新闻数据
    test_news = [
        {
            'title': '央行宣布降准0.5个百分点，释放流动性约1万亿元',
            'content': '中国人民银行今日宣布，决定于2024年1月15日下调金融机构存款准备金率0.5个百分点，此次降准将释放长期资金约1万亿元。央行表示，此举旨在保持银行体系流动性合理充裕，支持实体经济发展。',
            'source': '央行官网',
            'timestamp': datetime.now()
        },
        {
            'title': '美联储加息25个基点，符合市场预期',
            'content': '美联储宣布将联邦基金利率上调25个基点至5.25%-5.5%区间，这是今年第11次加息。美联储主席鲍威尔表示，通胀仍然过高，需要继续紧缩货币政策。',
            'source': '路透社',
            'timestamp': datetime.now()
        },
        {
            'title': '茅台股价创历史新高，市值突破3万亿',
            'content': '贵州茅台今日股价上涨3.2%，收盘价达到2180元，创历史新高。公司市值突破3万亿元大关，成为A股市值最高的公司。',
            'source': '财经网',
            'timestamp': datetime.now()
        },
        {
            'title': '新能源汽车销量同比增长35%',
            'content': '据中汽协数据，11月新能源汽车销量达到102.6万辆，同比增长35.1%。其中纯电动汽车销量为78.1万辆，插电式混合动力汽车销量为24.5万辆。',
            'source': '中汽协',
            'timestamp': datetime.now()
        },
        {
            'title': '某明星发布新专辑',
            'content': '某知名歌手今日发布了最新专辑，引发粉丝热议。专辑包含10首原创歌曲，风格多样。',
            'source': '娱乐新闻',
            'timestamp': datetime.now()
        }
    ]
    
    print("开始测试新闻分析器...")
    print(f"测试新闻数量: {len(test_news)}")
    print("\n" + "="*50)
    
    # 创建分析器实例
    analyzer = NewsAnalyzer()
    
    # 测试新的分析流程
    try:
        print("步骤1: 选择重要新闻...")
        selected_news = analyzer._select_important_news(test_news)
        print(f"选出重要新闻数量: {len(selected_news)}")
        for i, news in enumerate(selected_news, 1):
            print(f"  {i}. {news['title']}")
        
        print("\n" + "-"*50)
        print("步骤2: 生成AI摘要...")
        news_with_summaries = analyzer._generate_news_summaries(selected_news)
        print(f"生成摘要数量: {len(news_with_summaries)}")
        for i, news in enumerate(news_with_summaries, 1):
            print(f"  {i}. {news['title']}")
            print(f"     摘要: {news['ai_summary'][:100]}...")
        
        print("\n" + "-"*50)
        print("步骤3: 构建增强摘要...")
        enhanced_summary = analyzer._build_enhanced_news_summary(news_with_summaries)
        print("增强摘要预览:")
        print(enhanced_summary[:500] + "...")
        
        print("\n" + "-"*50)
        print("步骤4: 完整分析...")
        result = analyzer.analyze_news_impact(test_news)
        print("分析结果:")
        print(f"  影响程度: {result['impact_level']}")
        print(f"  紧急程度: {result['urgency']}")
        print(f"  分析: {result['analysis'][:200]}...")
        print(f"  建议: {result['recommendation'][:200]}...")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_news_analyzer()
