import akshare as ak
import pandas as pd
import logging
from datetime import datetime, timedelta
import time
import tushare as ts
from config import config

class StockDataFetcher:
    """股指数据获取类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.cache = {}  # 简单缓存机制
        self.cache_timeout = 300  # 5分钟缓存
        # 初始化 Tushare
        ts.set_token(config.TUSHARE_TOKEN)
        self.pro = ts.pro_api()
    
    def get_a_share_indices(self):
        """获取A股主要指数数据"""
        try:
            # 获取实时股指数据
            index_data = ak.stock_zh_index_spot_em()
            
            # 筛选主要指数
            main_indices = ['上证指数', '深证成指', '创业板指', '沪深300', '中证500']
            filtered_data = index_data[index_data['名称'].isin(main_indices)]
            
            result = []
            for _, row in filtered_data.iterrows():
                result.append({
                    'name': row['名称'],
                    'code': row['代码'],
                    'current_price': float(row['最新价']),
                    'change_pct': float(row['涨跌幅']),
                    'change_amount': float(row['涨跌额']),
                    'volume': row['成交量'],
                    'timestamp': datetime.now()
                })
            
            self.logger.info(f"成功获取A股指数数据 {len(result)} 条")
            return result
            
        except Exception as e:
            self.logger.error(f"获取A股指数数据失败: {e}")
            return []
    
    def get_international_indices(self):
        """获取国际股指数据"""
        try:
            result = []
            
            # 获取美股指数
            try:
                us_indices = ak.index_us_stock_sina()
                if not us_indices.empty:
                    latest_us = us_indices.iloc[-1]
                    result.append({
                        'name': '标普500',
                        'market': '美股',
                        'current_price': float(latest_us['close']),
                        'change_pct': self._calculate_change_pct(latest_us),
                        'timestamp': datetime.now()
                    })
            except Exception as e:
                self.logger.warning(f"获取美股数据失败: {e}")
            
            # 获取港股指数
            try:
                hk_indices = ak.stock_hk_index_spot_em()
                hsi_data = hk_indices[hk_indices['名称'].str.contains('恒生指数', na=False)]
                if not hsi_data.empty:
                    hsi = hsi_data.iloc[0]
                    result.append({
                        'name': '恒生指数',
                        'market': '港股',
                        'current_price': float(hsi['最新价']),
                        'change_pct': float(hsi['涨跌幅']),
                        'timestamp': datetime.now()
                    })
            except Exception as e:
                self.logger.warning(f"获取港股数据失败: {e}")
            
            # 获取A50期货数据（使用Tushare）
            try:
                # 获取当前日期
                today = datetime.now().strftime('%Y%m%d')
                
                # 获取A50期货数据
                a50_data = self.pro.fut_daily(ts_code='CNF0.SGX', 
                                            start_date=today,
                                            end_date=today)
                
                if not a50_data.empty:
                    latest_a50 = a50_data.iloc[0]
                    # 计算涨跌幅
                    change_pct = ((latest_a50['close'] - latest_a50['pre_close']) / latest_a50['pre_close']) * 100
                    
                    result.append({
                        'name': 'A50期货',
                        'market': '期货',
                        'current_price': float(latest_a50['close']),
                        'change_pct': float(change_pct),
                        'timestamp': datetime.now()
                    })
            except Exception as e:
                self.logger.warning(f"获取A50期货数据失败: {e}")
            
            self.logger.info(f"成功获取国际指数数据 {len(result)} 条")
            return result
            
        except Exception as e:
            self.logger.error(f"获取国际指数数据失败: {e}")
            return []
    
    def _calculate_change_pct(self, data_row):
        """计算涨跌幅"""
        try:
            if 'close' in data_row and 'open' in data_row:
                return ((data_row['close'] - data_row['open']) / data_row['open']) * 100
            return 0.0
        except:
            return 0.0
    
    def get_all_market_data(self):
        """获取所有市场数据"""
        all_data = {
            'a_share_indices': self.get_a_share_indices(),
            'international_indices': self.get_international_indices(),
            'timestamp': datetime.now()
        }
        return all_data

class StockAnalyzer:
    """股指数据分析类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.historical_data = []  # 存储历史数据用于分析
    
    def analyze_market_changes(self, current_data, historical_data=None):
        """分析市场变化"""
        try:
            analysis_result = {
                'alerts': [],
                'summary': '',
                'urgency': '低',
                'recommendations': []
            }
            
            # 分析A股指数
            a_share_alerts = self._analyze_a_share_indices(current_data.get('a_share_indices', []))
            analysis_result['alerts'].extend(a_share_alerts)
            
            # 分析国际市场影响
            international_alerts = self._analyze_international_impact(current_data.get('international_indices', []))
            analysis_result['alerts'].extend(international_alerts)
            
            # 确定整体紧急程度
            if analysis_result['alerts']:
                urgency_levels = [alert.get('urgency', '低') for alert in analysis_result['alerts']]
                if '紧急' in urgency_levels:
                    analysis_result['urgency'] = '紧急'
                elif '高' in urgency_levels:
                    analysis_result['urgency'] = '高'
                elif '中等' in urgency_levels:
                    analysis_result['urgency'] = '中等'
            
            # 生成总结
            analysis_result['summary'] = self._generate_summary(analysis_result['alerts'])
            
            # 生成建议
            analysis_result['recommendations'] = self._generate_recommendations(analysis_result['alerts'])
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"市场数据分析失败: {e}")
            return {
                'alerts': [],
                'summary': f'分析失败: {str(e)}',
                'urgency': '中等',
                'recommendations': ['建议人工检查市场情况']
            }
    
    def _analyze_a_share_indices(self, indices_data):
        """分析A股指数"""
        alerts = []
        
        for index in indices_data:
            try:
                name = index['name']
                change_pct = index['change_pct']
                
                # 检查日跌幅阈值
                if change_pct <= -config.DAILY_DROP_THRESHOLD:
                    alerts.append({
                        'type': '股指预警',
                        'title': f'{name}大幅下跌',
                        'message': f'{name}当日跌幅达到{change_pct:.2f}%，超过预警阈值{config.DAILY_DROP_THRESHOLD}%',
                        'urgency': '高' if change_pct <= -5.0 else '中等',
                        'index_name': name,
                        'change_pct': change_pct
                    })
                
                # 检查大幅上涨（可能的反弹信号）
                elif change_pct >= 3.0:
                    alerts.append({
                        'type': '股指提醒',
                        'title': f'{name}大幅上涨',
                        'message': f'{name}当日涨幅达到{change_pct:.2f}%，可能存在反弹机会',
                        'urgency': '低',
                        'index_name': name,
                        'change_pct': change_pct
                    })
                
            except Exception as e:
                self.logger.warning(f"分析指数数据失败 {index}: {e}")
        
        return alerts
    
    def _analyze_international_impact(self, international_data):
        """分析国际市场影响"""
        alerts = []
        
        for market in international_data:
            try:
                name = market['name']
                change_pct = market['change_pct']
                market_name = market['market']
                
                # 美股大跌可能影响A股
                if market_name == '美股' and change_pct <= -2.0:
                    alerts.append({
                        'type': '国际市场预警',
                        'title': f'{name}大跌可能影响A股',
                        'message': f'{name}跌幅{change_pct:.2f}%，可能对明日A股开盘造成负面影响',
                        'urgency': '中等',
                        'market': market_name,
                        'change_pct': change_pct
                    })
                
                # 港股大跌
                elif market_name == '港股' and change_pct <= -3.0:
                    alerts.append({
                        'type': '国际市场预警',
                        'title': f'{name}大跌',
                        'message': f'{name}跌幅{change_pct:.2f}%，可能影响A股相关板块',
                        'urgency': '中等',
                        'market': market_name,
                        'change_pct': change_pct
                    })
                
                # A50期货大跌
                elif 'A50' in name and change_pct <= -2.0:
                    alerts.append({
                        'type': '期货预警',
                        'title': f'{name}大跌',
                        'message': f'{name}跌幅{change_pct:.2f}%，预示A股可能承压',
                        'urgency': '高',
                        'market': market_name,
                        'change_pct': change_pct
                    })
                
            except Exception as e:
                self.logger.warning(f"分析国际市场数据失败 {market}: {e}")
        
        return alerts
    
    def _generate_summary(self, alerts):
        """生成分析总结"""
        if not alerts:
            return "当前市场运行平稳，暂无异常情况。"
        
        alert_types = {}
        for alert in alerts:
            alert_type = alert['type']
            if alert_type not in alert_types:
                alert_types[alert_type] = 0
            alert_types[alert_type] += 1
        
        summary_parts = []
        for alert_type, count in alert_types.items():
            summary_parts.append(f"{alert_type} {count} 条")
        
        return f"检测到市场异常情况：{', '.join(summary_parts)}。建议密切关注市场动态。"
    
    def _generate_recommendations(self, alerts):
        """生成投资建议"""
        if not alerts:
            return ["市场运行正常，可维持现有仓位"]
        
        recommendations = []
        high_urgency_count = sum(1 for alert in alerts if alert.get('urgency') == '高')
        medium_urgency_count = sum(1 for alert in alerts if alert.get('urgency') == '中等')
        
        if high_urgency_count > 0:
            recommendations.append("建议立即减仓，控制风险")
            recommendations.append("密切关注市场动态，准备进一步操作")
        elif medium_urgency_count > 1:
            recommendations.append("建议适当减仓，降低仓位")
            recommendations.append("关注后续市场表现")
        else:
            recommendations.append("建议保持谨慎，观望为主")
        
        return recommendations
    
    def should_trigger_alert(self, analysis_result):
        """判断是否应该触发预警"""
        alerts = analysis_result.get('alerts', [])
        urgency = analysis_result.get('urgency', '低')
        
        # 如果有预警信息或紧急程度较高，则触发预警
        if alerts or urgency in ['高', '紧急']:
            return True
        
        return False

# 全局实例
stock_data_fetcher = StockDataFetcher()
stock_analyzer = StockAnalyzer()

