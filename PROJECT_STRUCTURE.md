# 股票预警系统项目结构

```
stock_alert_system/
├── README.md                 # 使用说明文档
├── requirements.txt          # Python依赖包列表
├── .env.example             # 环境变量配置模板
├── deploy.sh                # 部署脚本
├── main.py                  # 系统主程序
├── config.py                # 配置管理模块
├── news_fetcher.py          # 新闻数据抓取模块
├── news_analyzer.py         # 新闻智能分析模块
├── stock_monitor.py         # 股指数据监控模块
├── email_sender.py          # 邮件发送服务模块
├── scheduler.py             # 定时任务调度模块
├── test_system.py           # 系统测试脚本
└── stock_alert.log          # 系统运行日志
```

## 核心文件说明

### 主程序文件
- **main.py**: 系统启动入口，负责系统初始化、信号处理和主循环控制
- **config.py**: 配置管理，读取环境变量并提供全局配置访问

### 功能模块
- **news_fetcher.py**: 从新浪财经、东方财富等网站抓取相关新闻
- **news_analyzer.py**: 使用Azure OpenAI分析新闻对股市的影响
- **stock_monitor.py**: 获取股指数据并进行变化分析
- **email_sender.py**: 发送预警邮件和系统通知
- **scheduler.py**: 管理定时任务，协调各模块工作

### 配置和部署
- **.env.example**: 环境变量配置模板
- **requirements.txt**: Python依赖包列表
- **deploy.sh**: 自动化部署脚本

### 测试和维护
- **test_system.py**: 单元测试和集成测试脚本
- **stock_alert.log**: 系统运行日志文件

## 系统特点

1. **模块化设计**: 各功能模块独立，便于维护和扩展
2. **配置化管理**: 通过环境变量灵活配置系统参数
3. **智能分析**: 集成Azure OpenAI进行新闻影响分析
4. **多源数据**: 支持多个新闻源和股票数据源
5. **定时监控**: 支持多种定时任务和预警机制
6. **邮件通知**: 自动发送预警邮件和系统状态通知
7. **错误处理**: 完善的异常处理和日志记录
8. **测试覆盖**: 包含单元测试和集成测试

## 技术栈

- **Python 3.11**: 主要开发语言
- **akshare**: 股票数据获取
- **requests + BeautifulSoup**: 网页数据抓取
- **Azure OpenAI**: 智能新闻分析
- **schedule**: 定时任务调度
- **smtplib**: 邮件发送
- **logging**: 日志记录
- **unittest**: 单元测试

