#!/usr/bin/env python3
"""
股票预警系统主程序
"""

import sys
import os
import signal
import logging
from datetime import datetime

from news_analyzer import news_analyzer

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import config
from scheduler import task_scheduler
from email_sender import email_sender

class StockAlertSystem:
    """股票预警系统主类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.running = False
        
    def start(self):
        """启动系统"""
        try:
            self.logger.info("=" * 50)
            self.logger.info("股票预警系统启动中...")
            self.logger.info("=" * 50)
            
            # 验证配置
            self.logger.info("验证系统配置...")
            config.validate_config()
            self.logger.info("配置验证通过")
            
            # 发送启动通知邮件
            self._send_startup_notification()
            
            # 启动任务调度器
            self.logger.info("启动任务调度器...")
            task_scheduler.start()
            
            self.running = True
            self.logger.info("股票预警系统启动成功！")
            self.logger.info("系统正在监控中，按 Ctrl+C 停止...")
            
            # 注册信号处理器
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
            
            # 主循环
            self._main_loop()
            
        except Exception as e:
            self.logger.error(f"系统启动失败: {e}")
            sys.exit(1)
    
    def stop(self):
        """停止系统"""
        if not self.running:
            return
        
        self.logger.info("正在停止股票预警系统...")
        
        # 停止任务调度器
        task_scheduler.stop()
        
        # 发送停止通知邮件
        self._send_shutdown_notification()
        
        self.running = False
        self.logger.info("股票预警系统已停止")
    
    def _main_loop(self):
        """主循环"""
        try:
            while self.running:
                # 主线程保持运行，让调度器在后台工作
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("接收到停止信号")
        finally:
            self.stop()
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"接收到信号 {signum}，准备停止系统...")
        self.running = False
    
    def _send_startup_notification(self):
        """发送启动通知邮件"""
        try:
            content = f"""
股票预警系统已成功启动！

启动时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

系统配置：
• 新闻检查间隔：{config.NEWS_CHECK_INTERVAL} 分钟
• 股指检查间隔：{config.STOCK_CHECK_INTERVAL} 分钟
• 日跌幅预警阈值：{config.DAILY_DROP_THRESHOLD}%
• 短期跌幅预警阈值：{config.SHORT_DROP_THRESHOLD}%

监控范围：
• 新闻源：新浪财经、东方财富
• 股指：上证指数、深证成指、创业板指、沪深300、中证500
• 国际市场：美股、港股、A50期货

系统将自动监控市场动态，如有异常情况将及时发送预警邮件。

祝您投资顺利！
"""
            
            email_sender.send_alert_email(
                alert_type="系统通知",
                title="股票预警系统启动成功",
                content=content,
                urgency="低"
            )
            
            self.logger.info("启动通知邮件发送成功")
            
        except Exception as e:
            self.logger.warning(f"发送启动通知邮件失败: {e}")
    
    def _send_shutdown_notification(self):
        """发送停止通知邮件"""
        try:
            content = f"""
股票预警系统已停止运行。

停止时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

如需重新启动系统，请运行：
python3 main.py

感谢您使用股票预警系统！
"""
            
            email_sender.send_alert_email(
                alert_type="系统通知",
                title="股票预警系统已停止",
                content=content,
                urgency="低"
            )
            
            self.logger.info("停止通知邮件发送成功")
            
        except Exception as e:
            self.logger.warning(f"发送停止通知邮件失败: {e}")

def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--test-email":
            # 测试邮件发送功能
            print("测试邮件发送功能...")
            try:
                config.validate_config()
                success = email_sender.send_test_email()
                if success:
                    print("✅ 邮件发送测试成功！")
                else:
                    print("❌ 邮件发送测试失败！")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            return
        
        elif sys.argv[1] == "--test-news":
            # 测试新闻抓取功能
            print("测试新闻抓取功能...")
            try:
                from news_fetcher import news_fetcher
                news_list = news_fetcher.fetch_all_news()
                analysis_result = news_analyzer.analyze_news_impact(news_list)
                print(f"✅ 成功抓取 {len(news_list)} 条新闻")
                for i, news in enumerate(news_list[:3], 1):
                    print(f"{i}. {news['title']} ({news['source']})")
                print(f"分析结果: {analysis_result}")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            return
        
        elif sys.argv[1] == "--test-stock":
            # 测试股票数据获取功能
            print("测试股票数据获取功能...")
            try:
                from stock_monitor import stock_data_fetcher
                market_data = stock_data_fetcher.get_all_market_data()
                a_share_count = len(market_data.get('a_share_indices', []))
                international_count = len(market_data.get('international_indices', []))
                print(f"✅ 成功获取A股指数 {a_share_count} 条，国际指数 {international_count} 条")
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            return
        
        elif sys.argv[1] == "--help":
            print("""
股票预警系统使用说明：

基本命令：
  python3 main.py                启动股票预警系统
  python3 main.py --help         显示帮助信息

测试命令：
  python3 main.py --test-email   测试邮件发送功能
  python3 main.py --test-news    测试新闻抓取功能
  python3 main.py --test-stock   测试股票数据获取功能

配置文件：
  请确保 .env 文件已正确配置所有必要参数
  参考 .env.example 文件进行配置
""")
            return
    
    # 启动主系统
    system = StockAlertSystem()
    system.start()

if __name__ == "__main__":
    main()

