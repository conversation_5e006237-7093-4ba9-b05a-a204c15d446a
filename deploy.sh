#!/bin/bash

# 股票预警系统部署脚本

echo "========================================"
echo "股票预警系统部署脚本"
echo "========================================"

# 检查Python版本
echo "检查Python版本..."
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装，请先安装Python3"
    exit 1
fi

# 检查pip
echo "检查pip..."
pip3 --version
if [ $? -ne 0 ]; then
    echo "❌ pip3未安装，请先安装pip3"
    exit 1
fi

# 安装依赖包
echo "安装依赖包..."
pip3 install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "❌ 依赖包安装失败"
    exit 1
fi

# 检查配置文件
echo "检查配置文件..."
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env配置文件"
    echo "请复制.env.example为.env并填写配置信息："
    echo "cp .env.example .env"
    echo "然后编辑.env文件填写必要的配置参数"
    exit 1
fi

# 运行测试
echo "运行系统测试..."
python3 test_system.py
if [ $? -ne 0 ]; then
    echo "❌ 系统测试失败，请检查配置"
    exit 1
fi

# 测试邮件功能
echo "测试邮件发送功能..."
python3 main.py --test-email
if [ $? -ne 0 ]; then
    echo "⚠️  邮件测试失败，请检查邮件配置"
fi

# 创建systemd服务文件（可选）
echo "创建systemd服务文件..."
cat > stock-alert.service << EOF
[Unit]
Description=Stock Alert System
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/python3 $(pwd)/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "✅ 部署完成！"
echo ""
echo "使用说明："
echo "1. 手动启动：python3 main.py"
echo "2. 测试功能："
echo "   - 测试邮件：python3 main.py --test-email"
echo "   - 测试新闻：python3 main.py --test-news"
echo "   - 测试股票：python3 main.py --test-stock"
echo "3. 安装为系统服务（可选）："
echo "   sudo cp stock-alert.service /etc/systemd/system/"
echo "   sudo systemctl enable stock-alert"
echo "   sudo systemctl start stock-alert"
echo ""
echo "注意：请确保.env文件中的配置信息正确填写！"

